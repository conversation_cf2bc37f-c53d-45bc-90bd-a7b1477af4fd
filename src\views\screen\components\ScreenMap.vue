<!--
 * @Description: 大屏地图组件示例
 * @Author: XIAOLIJUN
 * @Date: 2025-08-19 10:10:00
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2025-08-19 16:33:15
-->
<template>
  <div :class="prefixCls">
    <!-- Cesium 地图容器 -->
    <MapEngine
      v-if="showMap"
      ref="mapEngineRef"
      :imageryProvider="false"
      @ready="handleViewerReady"
    >
      <vc-layer-imagery>
        <vc-imagery-provider-urltemplate
          :projection-transforms="{ from: 'GCJ02', to: 'WGS84' }"
          :url="url"
        />
      </vc-layer-imagery>
    </MapEngine>

    <!-- 地图加载状态 -->
    <div v-if="!mapReady" :class="`${prefixCls}-loading`">
      <div :class="`${prefixCls}-loading-content`">
        <div :class="`${prefixCls}-loading-spinner`"></div>
        <div :class="`${prefixCls}-loading-text`">地图加载中...</div>
      </div>
    </div>

    <!-- 地图控制面板 -->
    <div v-if="mapReady" :class="`${prefixCls}-controls`">
      <div :class="`${prefixCls}-scale-info`"> 缩放比例: {{ (scale * 100).toFixed(1) }}% </div>
      <div :class="`${prefixCls}-resolution-info`">
        分辨率: {{ screenWidth }} × {{ screenHeight }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onUnmounted, inject, watch } from 'vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { MapEngine } from '/@/components/MapEngine';
  import { useCesiumMapScale } from '/@/hooks/web/useScreenScale';
  import { setViewerTheme } from '/@/components/MapEngine/src/tools';
  import { VcLayerImagery, VcImageryProviderAmap, VcImageryProviderUrltemplate } from 'vue-cesium';

  const { prefixCls } = useDesign('screen-map');

  // 注入父组件的缩放数据
  const scale = inject<any>('scale', ref(1));
  const screenWidth = inject<any>('screenWidth', ref(1920));
  const screenHeight = inject<any>('screenHeight', ref(1080));
  const onMapResize = inject<any>('onMapResize', () => {});
  const offMapResize = inject<any>('offMapResize', () => {});

  // 组件状态
  const showMap = ref(true);
  const mapReady = ref(false);
  const mapEngineRef = ref<any>();
  const cesiumViewer = ref<any>(null);

  // 地图缩放清理函数
  let cleanupMapScale: (() => void) | null = null;

  const url =
    'https://webrd02.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=2&style=8&x={x}&y={y}&z={z}';

  // Cesium Viewer 就绪回调
  const handleViewerReady = ({ viewer }) => {
    mapReady.value = true;
    console.log('Cesium Viewer 就绪', viewer);
    cesiumViewer.value = viewer;

    // 设置地图缩放适配
    if (onMapResize && typeof onMapResize === 'function') {
      cleanupMapScale = useCesiumMapScale(viewer, {
        onMapResize,
        offMapResize,
        getScale: () => scale.value,
      } as any);
    }

    // 初始化地图设置
    initMapSettings(viewer);
  };

  // 初始化地图设置
  const initMapSettings = (viewer: any) => {
    try {
      // 设置地图主题
      setViewerTheme(viewer);

      // viewer.scene.globe.baseColor = Cesium.Color.fromBytes(11, 68, 134, 1);
      // // 可选：禁用日照效果，使颜色更纯粹
      // viewer.scene.globe.enableLighting = false;
      // // 可选：禁用天空盒，避免默认的天空渐变和星星
      // viewer.scene.skyBox.show = false;
      // // 可选：禁用太阳和月亮（如果你完全不需要任何光源除了你自己的）
      // viewer.scene.sun.show = false;
      // viewer.scene.moon.show = false;

      // 禁用默认的双击行为
      viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
        viewer.scene.screenSpaceCameraController.enableInputs &&
          viewer.scene.screenSpaceCameraController.enableInputs.doubleClick,
      );
    } catch (error) {
      console.warn('地图初始化设置失败:', error);
    }
  };

  // 监听缩放变化
  watch(scale, (newScale) => {
    if (cesiumViewer.value && typeof newScale === 'number') {
      // 根据缩放比例调整地图渲染质量
      // try {
      //   if (newScale < 0.5) {
      //     cesiumViewer.value.resolutionScale = Math.max(0.5, newScale);
      //   } else {
      //     cesiumViewer.value.resolutionScale = 1.0;
      //   }
      //   cesiumViewer.value.scene.requestRender();
      // } catch (error) {
      //   console.warn('地图缩放调整失败:', error);
      // }
    }
  });

  // 组件卸载时清理
  onUnmounted(() => {
    if (cleanupMapScale) {
      cleanupMapScale();
    }
  });

  // 暴露方法
  defineExpose({
    getViewer: () => cesiumViewer.value,
    getMapEngine: () => mapEngineRef.value,
    refreshMap: () => {
      if (cesiumViewer.value) {
        cesiumViewer.value.resize();
        cesiumViewer.value.scene.requestRender();
      }
    },
  });
</script>

<style lang="less">
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  // 响应式调整
  @media screen and (max-width: 1366px) {
    .@{prefix-cls} {
      &-controls {
        top: 12px;
        right: 12px;
        padding: 8px;
        font-size: 12px;
      }
    }
  }

  .@{prefix-cls} {
    width: 100%;
    height: 100%;
    position: relative;

    &-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;

      &-content {
        text-align: center;
        color: #fff;
      }

      &-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(64, 158, 255, 0.3);
        border-top: 3px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 16px;
      }

      &-text {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.8);
      }
    }

    &-controls {
      position: absolute;
      top: 16px;
      right: 16px;
      background: rgba(16, 22, 58, 0.8);
      border: 1px solid rgba(64, 158, 255, 0.3);
      border-radius: 4px;
      padding: 12px;
      backdrop-filter: blur(10px);
      z-index: 100;
    }

    &-scale-info,
    &-resolution-info {
      color: #fff;
      font-size: 72px;
      line-height: 1.5;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    // 地图容器样式
    :deep(.engine-container) {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    // Cesium 相关样式调整
    :deep(.cesium-widget) {
      width: 100% !important;
      height: 100% !important;
    }

    :deep(.cesium-widget-credits) {
      display: none !important;
    }

    :deep(.cesium-navigation-container) {
      top: 60px !important;
      right: 16px !important;
    }
  }
  @prefix-cls: ~'@{namespace}-screen-map';
</style>
