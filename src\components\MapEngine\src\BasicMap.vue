<!--
 * @Description: 基础地图引擎
 * @Author: XIAOLIJUN
 * @Date: 2023-02-03 14:34:28
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2025-08-19 16:31:26
-->
<template>
  <div :class="['base-map', className]" :style="{ height, width }">
    <vc-config-provider :cesium-path="enginePath" :access-token="engineToken" :locale="locale">
      <slot name="before"></slot>
      <map-viewer v-bind="getBindValue">
        <slot></slot>
      </map-viewer>
      <slot name="after"></slot>
    </vc-config-provider>
  </div>
</template>

<script lang="ts">
  import { computed, defineComponent, unref } from 'vue';
  import { VcConfigProvider } from 'vue-cesium';
  import 'vue-cesium/dist/index.css';

  import MapViewer from './MapViewer.vue';

  import { useLocale } from '/@/locales/useLocale';
  import { useAppStore } from '/@/store/modules/app';
  import { LocaleType } from '/#/config';

  import zhCN from 'vue-cesium/es/locale/lang/zh-hans';
  import enUS from 'vue-cesium/es/locale/lang/en-us';

  export default defineComponent({
    name: 'ApiTransfer',
    components: { VcConfigProvider, MapViewer },
    inheritAttrs: false,
    props: {
      className: { type: String, default: 'engine-container' },
      width: { type: String, default: '100%' },
      height: { type: String, default: '100%' },
    },
    setup(props, { attrs }) {
      const { getLocale } = useLocale();
      const { localConfig } = useAppStore();

      const defaultOptions = {
        showCredit: false,
        removeCesiumScript: false,
        fullscreenButton: false,
        timeline: false,
        animation: false,
        infoBox: false,
        baseLayerPicker: false,
        imageryProvider: false,
        // infoBox: false,
        // skyBox: false,
        // contextOptions: { webgl: { alpha: true, antialias: true } },
        // skyAtmosphere: false,
        // selectionIndicator: false,
        // camera: { rectangle: [85, 47, 145, 20] }, // 限制地图显示范围
        // useBrowserRecommendedResolution: false,
        // showRenderLoopErrors: import.meta.env.DEV,
      };

      const getBindValue = computed(() => ({
        ...defaultOptions,
        ...unref(attrs),
        ...unref(props),
      }));

      const locale = computed(() => (unref(getLocale) === ('en' as LocaleType) ? enUS : zhCN));
      const { enginePath, engineToken } = localConfig;

      return { locale, enginePath, engineToken, getBindValue };
    },
  });
</script>

<style lang="less" scoped>
  .base-map {
    position: relative;
    overflow: hidden;
  }
</style>
