{"name": "smart-brain-center-web", "version": "0.1.0", "author": {"name": "ctfo", "email": "<EMAIL>"}, "license": "UNLICENSED", "scripts": {"bootstrap": "pnpm install", "serve": "npm run dev", "dev": "vite", "build": "cross-env NODE_ENV=production vite build && esno ./build/script/postBuild.ts", "build:test": "cross-env vite build --mode test && esno ./build/script/postBuild.ts", "build:no-cache": "pnpm clean:cache && npm run build", "report": "cross-env REPORT=true npm run build", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build && vite preview", "preview:dist": "vite preview", "log": "conventional-changelog -p angular -i CHANGELOG.md -s", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "test:unit": "jest", "test:gzip": "npx http-server dist --cors --gzip -c-1", "test:br": "npx http-server dist --cors --brotli -c-1", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "prepare": "husky install", "gen:icon": "esno ./build/generate/icon/index.ts"}, "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/icons-vue": "^6.1.0", "@iconify/iconify": "^2.2.1", "@logicflow/core": "^1.1.13", "@logicflow/extension": "^1.1.13", "@simonwep/pickr": "^1.8.2", "@turf/turf": "^6.5.0", "@vue/runtime-core": "^3.2.33", "@vue/shared": "^3.2.33", "@vueuse/core": "^8.3.0", "@vueuse/shared": "^8.3.0", "@zxcvbn-ts/core": "^2.0.1", "ant-design-vue": "^3.2.16", "axios": "^0.26.1", "chroma-js": "^2.4.2", "codemirror": "^5.65.3", "cropperjs": "^1.5.12", "crypto-js": "^4.1.1", "currency.js": "^2.0.4", "dayjs": "^1.11.1", "echarts": "^5.3.2", "intro.js": "^5.1.0", "jsencrypt": "^3.2.1", "lodash-es": "^4.17.21", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.0", "pdfjs-dist": "2.11.338", "pinia": "2.0.12", "print-js": "^1.6.0", "qrcode": "^1.5.0", "qs": "^6.10.3", "resize-observer-polyfill": "^1.5.1", "showdown": "^2.1.0", "sm-crypto": "^0.3.12", "sortablejs": "^1.15.0", "tinymce": "^5.10.3", "vditor": "^3.8.13", "vue": "~3.2.33", "vue-cesium": "^3.2.9", "vue-i18n": "9.2.2", "vue-json-pretty": "^2.0.6", "vue-router": "^4.5.1", "vue-types": "^4.1.1", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^16.2.3", "@commitlint/config-conventional": "^16.2.1", "@iconify/json": "^2.1.30", "@purge-icons/generated": "^0.8.1", "@types/codemirror": "^5.60.5", "@types/crypto-js": "^4.1.1", "@types/fs-extra": "^9.0.13", "@types/inquirer": "^8.2.1", "@types/intro.js": "^3.0.2", "@types/lodash-es": "^4.17.6", "@types/mockjs": "^1.0.6", "@types/node": "^17.0.25", "@types/nprogress": "^0.2.0", "@types/qrcode": "^1.4.2", "@types/qs": "^6.9.7", "@types/showdown": "^1.9.4", "@types/sortablejs": "^1.10.7", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "@vitejs/plugin-legacy": "^1.8.1", "@vitejs/plugin-vue": "^2.3.1", "@vitejs/plugin-vue-jsx": "^1.3.10", "@vue/compiler-sfc": "^3.2.33", "@vue/test-utils": "^2.0.0-rc.21", "autoprefixer": "^10.4.4", "cesium": "^1.132.0", "commitizen": "^4.2.4", "conventional-changelog-cli": "^2.2.2", "cross-env": "^7.0.3", "dotenv": "^16.0.0", "eslint": "^8.13.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.6.0", "esno": "^0.14.1", "fs-extra": "^10.1.0", "husky": "^7.0.4", "inquirer": "^8.2.2", "less": "^4.1.2", "lint-staged": "12.3.7", "npm-run-all": "^4.1.5", "picocolors": "^1.0.0", "postcss": "^8.4.12", "postcss-html": "^1.4.1", "postcss-less": "^6.0.0", "prettier": "^2.6.2", "rimraf": "^3.0.2", "rollup": "^2.70.2", "rollup-plugin-visualizer": "^5.6.0", "stylelint": "^14.7.1", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recommended": "^7.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^25.0.0", "stylelint-order": "^5.0.0", "ts-node": "^10.7.0", "typescript": "^4.6.3", "vite": "^2.9.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-files-copy": "^3.0.0", "vite-plugin-html": "^3.2.0", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-mkcert": "1.7.2", "vite-plugin-mock": "^2.9.6", "vite-plugin-purge-icons": "^0.8.1", "vite-plugin-pwa": "^0.11.13", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-theme": "^0.8.6", "vite-plugin-vue-setup-extend": "^0.4.0", "vite-plugin-windicss": "^1.8.4", "vue-eslint-parser": "^8.3.0", "vue-tsc": "^0.33.9"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "rollup": "^2.56.3", "gifsicle": "5.2.0"}, "repository": {"type": "git", "url": "git+https://git.ctfo.com/luwang/qffe/components/ctfo-web-admin-lite.git"}, "homepage": "https://git.ctfo.com/luwang/qffe/components/ctfo-web-admin-lite", "engines": {"node": ">=16"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,less,styl,html}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}}