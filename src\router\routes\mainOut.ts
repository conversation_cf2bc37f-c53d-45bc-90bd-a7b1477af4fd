/*
 * @Description:
 * @Author: XIAOLIJUN
 * @Date: 2025-08-18 17:14:13
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2025-08-19 10:14:34
 */
import type { AppRouteModule } from '/@/router/types';

export const mainOutRoutes: AppRouteModule[] = [
  {
    path: '/screen',
    name: 'Screen',
    component: () => import('/@/views/screen/index.vue'),
    meta: {
      title: '吉高股份智慧大脑云控中心',
      ignoreAuth: true,
    },
  },
];

export const mainOutRouteNames = mainOutRoutes.map((item) => item.name);
