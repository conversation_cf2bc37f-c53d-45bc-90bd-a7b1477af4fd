/*
 * @Description:
 * @Author: XIAOLIJUN
 * @Date: 2025-08-19 09:17:59
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2025-08-19 15:54:59
 */
import { Viewer, ImageryLayer } from 'cesium';

/**
 * 定义 setViewerTheme 函数的配置选项接口。
 */
interface SetViewerThemeOptions {
  brightness?: number;
  contrast?: number;
  gamma?: number;
  hue?: number;
  saturation?: number;
  invertColor?: boolean; // 如果为 true，则不反转颜色。如果为 false 或 undefined，则反转颜色。
  filterRGB_R?: number; // 红色通道滤镜值 (0-255)
  filterRGB_G?: number; // 绿色通道滤镜值 (0-255)
  filterRGB_B?: number; // 蓝色通道滤镜值 (0-255)
}

/**
 * 设置 Cesium Viewer 的主题，包括底图的视觉效果和全局着色器修改。
 * @param viewer Cesium Viewer 实例。
 * @param options 配置选项，用于调整底图属性和颜色滤镜。
 */
export function setViewerTheme(viewer: Viewer, options: SetViewerThemeOptions = {}): void {
  // 获取第一个影像图层（通常是底图）
  const baseLayer: ImageryLayer | undefined = viewer.imageryLayers.get(0);

  // 如果没有底图，则直接返回
  if (!baseLayer) {
    console.warn('没有找到底图图层，无法设置主题。');
    return;
  }

  // 设置底图的亮度、对比度、伽马、色调和饱和度
  // 使用空值合并运算符 (??) 提供默认值
  baseLayer.brightness = options.brightness ?? 0.6;
  baseLayer.contrast = options.contrast ?? 1.8;
  baseLayer.gamma = options.gamma ?? 0.3;
  baseLayer.hue = options.hue ?? 1;
  baseLayer.saturation = options.saturation ?? 0; // 默认值更改为0以匹配JS版本

  // 获取地球表面的基础片段着色器源码
  // 这里的类型断言 'any' 是因为 Cesium 的类型定义可能不会直接暴露这个私有属性，
  // 或者它是一个非常内部的实现细节。在生产代码中，如果可能，最好避免直接访问私有属性。
  // 如果 Cesium 未来版本更改了这些内部结构，这段代码可能会中断。
  const baseFragShader: string[] = (viewer.scene.globe as any)._surfaceShaderSet
    .baseFragmentShaderSource.sources;

  // 遍历所有片段着色器源码，修改颜色逻辑
  for (let i = 0; i < baseFragShader.length; i++) {
    const strS = 'color = czm_saturation(color, textureSaturation);\n#endif\n';
    let strT = strS; // 初始化为原始字符串

    // 如果 options.invertColor 为 false，则反转颜色
    // 注意：原始JS代码是 `!options.invertColor` 意味着当 invertColor 为 false/undefined/null 时执行反转。
    // 如果你希望只有当 invertColor 明确为 true 时才不反转，其他情况都反转，这个逻辑是对的。
    // 我假设你希望的是当 invertColor 为 true 时不反转，否则反转。
    // 如果你希望只有当 invertColor 明确为 true 时才反转，请将条件改为 `if (options.invertColor)`
    if (!options.invertColor) {
      strT += `
                color.r = 1.0 - color.r;
                color.g = 1.0 - color.g;
                color.b = 1.0 - color.b;
            `;
    }

    // 应用自定义 RGB 颜色滤镜
    // 注意：原始JS代码的默认值是 100, 138, 230。这里的类型是数字，所以直接使用。
    // 记得这些值在着色器中会被 255.0 除。
    const filterR = options.filterRGB_R ?? 13;
    const filterG = options.filterRGB_G ?? 99;
    const filterB = options.filterRGB_B ?? 134;

    strT += `
            color.r = color.r * ${filterR}.0/255.0;
            color.g = color.g * ${filterG}.0/255.0;
            color.b = color.b * ${filterB}.0/255.0;
        `;

    // 替换原始着色器中的匹配部分
    baseFragShader[i] = baseFragShader[i].replace(strS, strT);
  }

  // 请求重新渲染场景以应用着色器变化
  viewer.scene.requestRender();
}
